#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小说人物关系管理系统 Postman Collection 验证脚本

此脚本用于验证生成的Postman Collection JSON文件的完整性和正确性。
"""

import json
import sys
from typing import Dict, List, Any

def load_collection(file_path: str) -> Dict[str, Any]:
    """加载Postman Collection JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        sys.exit(1)

def validate_basic_structure(collection: Dict[str, Any]) -> bool:
    """验证基础结构"""
    print("🔍 验证基础结构...")
    
    required_fields = ['info', 'item', 'event', 'variable']
    for field in required_fields:
        if field not in collection:
            print(f"❌ 缺少必需字段: {field}")
            return False
    
    # 验证info字段
    info = collection['info']
    info_required = ['name', 'description', 'schema']
    for field in info_required:
        if field not in info:
            print(f"❌ info中缺少必需字段: {field}")
            return False
    
    # 验证schema
    expected_schema = "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    if info['schema'] != expected_schema:
        print(f"❌ Schema不正确: {info['schema']}")
        return False
    
    print("✅ 基础结构验证通过")
    return True

def validate_modules(collection: Dict[str, Any]) -> bool:
    """验证模块结构"""
    print("🔍 验证模块结构...")
    
    items = collection['item']
    if not isinstance(items, list):
        print("❌ item字段应该是数组")
        return False
    
    expected_modules = [
        "用户认证模块",
        "小说管理模块", 
        "角色管理模块",
        "管理员模块",
        "微信读书API代理"
    ]
    
    found_modules = [item['name'] for item in items if 'name' in item]
    
    for module in expected_modules:
        if module not in found_modules:
            print(f"⚠️  缺少模块: {module}")
    
    print(f"✅ 找到 {len(found_modules)} 个模块")
    return True

def validate_requests(collection: Dict[str, Any]) -> bool:
    """验证请求结构"""
    print("🔍 验证请求结构...")
    
    total_requests = 0
    valid_requests = 0
    
    def validate_request_item(item: Dict[str, Any], module_name: str = ""):
        nonlocal total_requests, valid_requests
        
        if 'request' in item:
            total_requests += 1
            request = item['request']
            
            # 验证必需字段
            required_fields = ['method', 'url']
            has_all_fields = all(field in request for field in required_fields)
            
            # 验证URL结构
            url_valid = False
            if 'url' in request:
                url = request['url']
                if isinstance(url, dict) and 'raw' in url:
                    url_valid = '{{base_url}}' in url['raw']
                elif isinstance(url, str):
                    url_valid = '{{base_url}}' in url
            
            # 验证HTTP方法
            method_valid = request.get('method') in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
            
            if has_all_fields and url_valid and method_valid:
                valid_requests += 1
            else:
                print(f"⚠️  请求验证失败: {item.get('name', 'Unknown')} in {module_name}")
        
        # 递归验证子项目
        if 'item' in item and isinstance(item['item'], list):
            for sub_item in item['item']:
                validate_request_item(sub_item, item.get('name', module_name))
    
    for item in collection['item']:
        validate_request_item(item)
    
    print(f"✅ 验证了 {total_requests} 个请求，其中 {valid_requests} 个有效")
    return valid_requests > 0

def validate_responses(collection: Dict[str, Any]) -> bool:
    """验证响应示例"""
    print("🔍 验证响应示例...")
    
    total_responses = 0
    
    def count_responses(item: Dict[str, Any]):
        nonlocal total_responses
        
        if 'response' in item and isinstance(item['response'], list):
            total_responses += len(item['response'])
        
        if 'item' in item and isinstance(item['item'], list):
            for sub_item in item['item']:
                count_responses(sub_item)
    
    for item in collection['item']:
        count_responses(item)
    
    print(f"✅ 找到 {total_responses} 个响应示例")
    return total_responses > 0

def validate_tests(collection: Dict[str, Any]) -> bool:
    """验证测试脚本"""
    print("🔍 验证测试脚本...")
    
    total_tests = 0
    
    def count_tests(item: Dict[str, Any]):
        nonlocal total_tests
        
        if 'event' in item and isinstance(item['event'], list):
            for event in item['event']:
                if event.get('listen') == 'test':
                    total_tests += 1
        
        if 'item' in item and isinstance(item['item'], list):
            for sub_item in item['item']:
                count_tests(sub_item)
    
    for item in collection['item']:
        count_tests(item)
    
    # 检查全局测试脚本
    if 'event' in collection:
        for event in collection['event']:
            if event.get('listen') == 'test':
                total_tests += 1
    
    print(f"✅ 找到 {total_tests} 个测试脚本")
    return total_tests > 0

def validate_variables(collection: Dict[str, Any]) -> bool:
    """验证环境变量"""
    print("🔍 验证环境变量...")
    
    variables = collection.get('variable', [])
    if not isinstance(variables, list):
        print("❌ variable字段应该是数组")
        return False
    
    expected_vars = ['base_url', 'api_version']
    found_vars = [var['key'] for var in variables if 'key' in var]
    
    for var in expected_vars:
        if var not in found_vars:
            print(f"⚠️  缺少环境变量: {var}")
    
    print(f"✅ 找到 {len(found_vars)} 个环境变量")
    return len(found_vars) > 0

def generate_report(collection: Dict[str, Any]) -> None:
    """生成验证报告"""
    print("\n" + "="*50)
    print("📊 验证报告")
    print("="*50)
    
    # 统计信息
    def count_items(items: List[Dict[str, Any]]) -> Dict[str, int]:
        stats = {'modules': 0, 'requests': 0, 'responses': 0, 'tests': 0}
        
        for item in items:
            if 'item' in item and isinstance(item['item'], list):
                stats['modules'] += 1
                sub_stats = count_items(item['item'])
                stats['requests'] += sub_stats['requests']
                stats['responses'] += sub_stats['responses'] 
                stats['tests'] += sub_stats['tests']
            else:
                stats['requests'] += 1
                if 'response' in item:
                    stats['responses'] += len(item['response'])
                if 'event' in item:
                    stats['tests'] += len([e for e in item['event'] if e.get('listen') == 'test'])
        
        return stats
    
    stats = count_items(collection['item'])
    
    print(f"📁 模块数量: {stats['modules']}")
    print(f"🔗 API端点数量: {stats['requests']}")
    print(f"📄 响应示例数量: {stats['responses']}")
    print(f"🧪 测试脚本数量: {stats['tests']}")
    print(f"🔧 环境变量数量: {len(collection.get('variable', []))}")
    
    # 文件信息
    print(f"\n📋 Collection信息:")
    info = collection['info']
    print(f"   名称: {info.get('name', 'N/A')}")
    print(f"   Schema: {info.get('schema', 'N/A')}")
    
    print("\n✅ Collection验证完成！")

def main():
    """主函数"""
    print("🚀 开始验证 Postman Collection...")
    print("="*50)
    
    file_path = "CharacterNetwork_API_Collection.json"
    collection = load_collection(file_path)
    
    # 执行验证
    validations = [
        validate_basic_structure(collection),
        validate_modules(collection),
        validate_requests(collection),
        validate_responses(collection),
        validate_tests(collection),
        validate_variables(collection)
    ]
    
    # 生成报告
    generate_report(collection)
    
    # 总结
    passed = sum(validations)
    total = len(validations)
    
    print(f"\n🎯 验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有验证项目都通过了！Collection可以正常使用。")
        sys.exit(0)
    else:
        print("⚠️  部分验证项目未通过，请检查上述警告信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
