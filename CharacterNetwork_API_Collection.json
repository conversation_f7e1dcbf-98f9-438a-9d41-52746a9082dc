{"info": {"name": "小说人物关系管理系统 API Collection", "description": "## 小说人物关系管理系统 API文档\n\n### 项目概述\n- **版本**: v1.0.0\n- **基础URL**: {{base_url}}\n- **认证方式**: Session-based Authentication (Passport.js)\n- **技术栈**: Express.js + TypeScript + PostgreSQL + Drizzle ORM\n\n### 核心功能\n- 用户认证与权限管理\n- 小说创建与管理\n- 角色信息管理\n- 人物关系网络构建\n- 时间线事件记录\n- 笔记功能\n- 外部书籍信息集成\n- 管理员系统管理\n\n### 认证说明\n- 使用Session认证，登录后会自动维持会话状态\n- 部分接口需要管理员权限\n- 文件上传接口支持图片格式：jpg, jpeg, png, gif\n- 文件大小限制：10MB\n\n### 开发者信息\n- 维护团队: CharacterNetwork开发团队\n- 联系方式: <EMAIL>\n- 文档更新时间: 2024年12月", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "用户认证模块", "description": "### 用户认证模块\n\n#### 业务逻辑\n- 用户注册与登录管理\n- Session会话维护\n- 密码安全处理\n- 权限验证机制\n\n#### 业务约束\n- 用户名和邮箱必须唯一\n- 密码使用scrypt加密存储\n- 第一个注册用户自动成为管理员\n- Session有效期为7天", "item": [{"name": "用户注册", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| username | string | 是 | 用户名，必须唯一 | \"testuser\" |\n| password | string | 是 | 密码，建议8位以上 | \"password123\" |\n| email | string | 是 | 邮箱地址，必须唯一 | \"<EMAIL>\" |"}}}, "url": {"raw": "{{base_url}}/api/register", "host": ["{{base_url}}"], "path": ["api", "register"]}, "description": "#### 用户注册\n\n**功能说明**: 创建新用户账户，第一个注册的用户自动获得管理员权限\n\n**权限要求**: \n- 角色：无需认证\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：10次/秒\n- 并发限制：5个/IP\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | 所有字段都是必需的 | 检查必填参数完整性 |\n| 400 | 400 | 用户名已存在 | 更换用户名 |\n| 400 | 400 | 邮箱已存在 | 更换邮箱地址 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 用户名和邮箱必须全局唯一\n- 密码会使用scrypt算法加密存储\n- 第一个注册用户自动成为系统管理员"}, "response": [{"name": "注册成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/register"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"isAdmin\": true,\n  \"createdAt\": \"2024-12-19T10:30:00.000Z\"\n}", "description": "### 注册成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 用户唯一标识符 |\n| username | string | 用户名 |\n| email | string | 邮箱地址 |\n| isAdmin | boolean | 是否为管理员 |\n| createdAt | string | 账户创建时间 |"}, {"name": "用户名已存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"existinguser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/register"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"用户名已存在\"\n}", "description": "### 用户名冲突响应说明\n\n**触发条件**:\n- 提交的用户名已被其他用户使用\n\n**处理建议**:\n- 提示用户更换用户名\n- 可以建议在用户名后添加数字"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect(pm.response.code).to.be.oneOf([201, 400]);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// 成功注册时的验证", "if (pm.response.code === 201) {", "    pm.test('注册成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('username');", "        pm.expect(jsonData).to.have.property('email');", "        pm.expect(jsonData).to.have.property('isAdmin');", "        pm.expect(jsonData).to.not.have.property('password');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证返回的用户信息不包含密码", "   - 检查用户ID为正整数", "   - 验证邮箱格式正确性", "*/"], "type": "text/javascript"}}]}, {"name": "用户登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}", "options": {"raw": {"language": "json", "description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| username | string | 是 | 用户名 | \"testuser\" |\n| password | string | 是 | 密码 | \"password123\" |"}}}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}, "description": "#### 用户登录\n\n**功能说明**: 用户身份验证，成功后建立Session会话\n\n**权限要求**: \n- 角色：无需认证\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：20次/秒\n- 并发限制：10个/IP\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | 用户名或密码错误 | 检查用户名和密码 |\n| 401 | 401 | 身份验证失败 | 确认账户状态正常 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 登录成功后会自动创建Session\n- Session有效期为7天\n- 支持并发登录"}, "response": [{"name": "登录成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/login"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Set-<PERSON><PERSON>", "value": "connect.sid=s%3A1234567890abcdef; Path=/; HttpOnly"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"isAdmin\": true,\n  \"createdAt\": \"2024-12-19T10:30:00.000Z\"\n}", "description": "### 登录成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 用户唯一标识符 |\n| username | string | 用户名 |\n| email | string | 邮箱地址 |\n| isAdmin | boolean | 是否为管理员 |\n| createdAt | string | 账户创建时间 |\n\n**注意事项**:\n- 响应头中包含Session Cookie\n- 后续请求会自动携带此Cookie"}, {"name": "登录失败", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/api/login"}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"用户名或密码错误\"\n}", "description": "### 登录失败响应说明\n\n**触发条件**:\n- 用户名不存在\n- 密码错误\n- 账户被禁用\n\n**处理建议**:\n- 提示用户检查用户名和密码\n- 提供找回密码功能"}], "event": [{"listen": "test", "script": {"exec": ["// 基础响应验证", "pm.test('HTTP状态码验证', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401]);", "});", "", "// 响应时间验证", "pm.test('响应时间验证', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// 登录成功时的验证", "if (pm.response.code === 200) {", "    pm.test('登录成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('username');", "        pm.expect(jsonData).to.have.property('email');", "        pm.expect(jsonData).to.have.property('isAdmin');", "        pm.expect(jsonData).to.not.have.property('password');", "    });", "    ", "    pm.test('Session <PERSON><PERSON>验证', function () {", "        pm.expect(pm.response.headers.get('Set-<PERSON><PERSON>')).to.include('connect.sid');", "    });", "}", "", "/* 业务逻辑验证：", "   - 验证Session Cookie的设置", "   - 检查用户权限信息", "   - 验证响应不包含敏感信息", "*/"], "type": "text/javascript"}}]}, {"name": "用户登出", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": "请求内容类型"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/logout", "host": ["{{base_url}}"], "path": ["api", "logout"]}, "description": "#### 用户登出\n\n**功能说明**: 销毁当前用户的Session会话\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效Session\n\n**业务规则**:\n- 清除服务器端Session数据\n- 客户端应清除相关Cookie"}, "response": [{"name": "登出成功", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/logout"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"登出成功\"\n}", "description": "### 登出成功响应说明\n\n**响应说明**:\n- Session已被销毁\n- 后续请求需要重新登录"}], "event": [{"listen": "test", "script": {"exec": ["pm.test('登出成功验证', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql('登出成功');", "});"], "type": "text/javascript"}}]}, {"name": "获取当前用户信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user", "host": ["{{base_url}}"], "path": ["api", "user"]}, "description": "#### 获取当前用户信息\n\n**功能说明**: 获取当前登录用户的详细信息\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效Session\n\n**业务规则**:\n- 返回当前Session对应的用户信息\n- 不返回密码等敏感信息"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 1,\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"isAdmin\": true,\n  \"createdAt\": \"2024-12-19T10:30:00.000Z\"\n}", "description": "### 用户信息响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 用户唯一标识符 |\n| username | string | 用户名 |\n| email | string | 邮箱地址 |\n| isAdmin | boolean | 是否为管理员 |\n| createdAt | string | 账户创建时间 |"}, {"name": "未登录", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user"}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Unauthorized\"\n}", "description": "### 未登录响应说明\n\n**触发条件**:\n- 没有有效的Session\n- Session已过期\n\n**处理建议**:\n- 重定向到登录页面\n- 提示用户重新登录"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查是否已登录", "if (!pm.cookies.has('connect.sid')) {", "    console.log('警告：未检测到Session Cookie，请先登录');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('响应状态验证', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('用户信息格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('username');", "        pm.expect(jsonData).to.have.property('email');", "        pm.expect(jsonData).to.not.have.property('password');", "    });", "}"], "type": "text/javascript"}}]}]}, {"name": "小说管理模块", "description": "### 小说管理模块\n\n#### 业务逻辑\n- 小说的创建、查看、编辑、删除\n- 封面图片上传和管理\n- 小说状态管理（进行中、已完成、暂停等）\n- 与外部书籍信息的关联\n\n#### 业务约束\n- 每个用户只能管理自己创建的小说\n- 管理员可以查看和管理所有小说\n- 封面图片大小限制10MB\n- 支持的图片格式：jpg, jpeg, png, gif", "item": [{"name": "获取用户小说列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels", "host": ["{{base_url}}"], "path": ["api", "novels"]}, "description": "#### 获取用户小说列表\n\n**功能说明**: 获取当前用户创建的所有小说\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效Session\n\n**限流规则**: \n- QPS限制：50次/秒\n- 并发限制：20个/用户\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | Unauthorized | 重新登录 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只返回当前用户创建的小说\n- 管理员可以看到所有用户的小说\n- 按创建时间倒序排列"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/novels"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"title\": \"测试小说\",\n    \"description\": \"这是一个测试小说的描述\",\n    \"coverImage\": \"/uploads/novel-cover-123.jpg\",\n    \"genre\": \"奇幻\",\n    \"status\": \"In Progress\",\n    \"userId\": 1,\n    \"bookInfoId\": null,\n    \"createdAt\": \"2024-12-19T10:30:00.000Z\",\n    \"updatedAt\": \"2024-12-19T10:30:00.000Z\"\n  },\n  {\n    \"id\": 2,\n    \"title\": \"另一个小说\",\n    \"description\": \"另一个小说的描述\",\n    \"coverImage\": null,\n    \"genre\": \"科幻\",\n    \"status\": \"Completed\",\n    \"userId\": 1,\n    \"bookInfoId\": 5,\n    \"createdAt\": \"2024-12-18T15:20:00.000Z\",\n    \"updatedAt\": \"2024-12-19T09:15:00.000Z\"\n  }\n]", "description": "### 小说列表响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 小说唯一标识符 |\n| title | string | 小说标题 |\n| description | string | 小说描述 |\n| coverImage | string\\|null | 封面图片路径 |\n| genre | string | 小说类型 |\n| status | string | 小说状态 |\n| userId | number | 创建者用户ID |\n| bookInfoId | number\\|null | 关联的书籍信息ID |\n| createdAt | string | 创建时间 |\n| updatedAt | string | 更新时间 |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查认证状态", "if (!pm.cookies.has('connect.sid')) {", "    console.log('警告：未检测到Session Cookie，请先登录');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('获取小说列表成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    ", "    if (jsonData.length > 0) {", "        const novel = jsonData[0];", "        pm.expect(novel).to.have.property('id');", "        pm.expect(novel).to.have.property('title');", "        pm.expect(novel).to.have.property('userId');", "        pm.expect(novel).to.have.property('createdAt');", "    }", "});", "", "/* 业务逻辑验证：", "   - 验证返回的小说都属于当前用户", "   - 检查小说状态的有效性", "   - 验证时间格式正确性", "*/"], "type": "text/javascript"}}]}, {"name": "创建小说", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "我的新小说", "description": "小说标题（必填）", "type": "text"}, {"key": "description", "value": "这是一个关于冒险的故事...", "description": "小说描述（可选）", "type": "text"}, {"key": "genre", "value": "奇幻", "description": "小说类型（可选）", "type": "text"}, {"key": "status", "value": "In Progress", "description": "小说状态：In Progress, Completed, Paused（可选，默认In Progress）", "type": "text"}, {"key": "coverImage", "description": "封面图片文件（可选，支持jpg, jpeg, png, gif，最大10MB）", "type": "file", "src": []}, {"key": "coverImageUrl", "value": "https://example.com/cover.jpg", "description": "封面图片URL（可选，与coverImage二选一）", "type": "text", "disabled": true}], "options": {"formdata": {"description": "### 请求参数说明\n\n| 字段名 | 类型 | 必填 | 描述 | 示例值 |\n|--------|------|------|------|--------|\n| title | string | 是 | 小说标题 | \"我的新小说\" |\n| description | string | 否 | 小说描述 | \"这是一个关于冒险的故事...\" |\n| genre | string | 否 | 小说类型 | \"奇幻\" |\n| status | string | 否 | 小说状态 | \"In Progress\" |\n| coverImage | file | 否 | 封面图片文件 | 图片文件 |\n| coverImageUrl | string | 否 | 封面图片URL | \"https://example.com/cover.jpg\" |"}}}, "url": {"raw": "{{base_url}}/api/novels", "host": ["{{base_url}}"], "path": ["api", "novels"]}, "description": "#### 创建小说\n\n**功能说明**: 创建新的小说，支持封面图片上传\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：需要有效Session\n\n**限流规则**: \n- QPS限制：10次/秒\n- 并发限制：5个/用户\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | Invalid novel data | 检查请求参数格式 |\n| 401 | 401 | Unauthorized | 重新登录 |\n| 413 | 413 | File too large | 减小文件大小至10MB以下 |\n| 415 | 415 | Unsupported file type | 使用支持的图片格式 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 标题为必填字段\n- 封面图片支持jpg, jpeg, png, gif格式\n- 文件大小限制10MB\n- 可以使用文件上传或URL方式设置封面"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "我的新小说", "type": "text"}, {"key": "description", "value": "这是一个关于冒险的故事...", "type": "text"}, {"key": "genre", "value": "奇幻", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/novels"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": 3,\n  \"title\": \"我的新小说\",\n  \"description\": \"这是一个关于冒险的故事...\",\n  \"coverImage\": \"/uploads/novel-cover-abc123.jpg\",\n  \"genre\": \"奇幻\",\n  \"status\": \"In Progress\",\n  \"userId\": 1,\n  \"bookInfoId\": null,\n  \"createdAt\": \"2024-12-19T11:00:00.000Z\",\n  \"updatedAt\": \"2024-12-19T11:00:00.000Z\"\n}", "description": "### 创建成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 新创建的小说ID |\n| title | string | 小说标题 |\n| description | string | 小说描述 |\n| coverImage | string\\|null | 封面图片路径 |\n| genre | string | 小说类型 |\n| status | string | 小说状态 |\n| userId | number | 创建者用户ID |\n| bookInfoId | number\\|null | 关联的书籍信息ID |\n| createdAt | string | 创建时间 |\n| updatedAt | string | 更新时间 |"}, {"name": "参数验证失败", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "description", "value": "缺少标题的小说", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/novels"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Invalid novel data\",\n  \"errors\": {\n    \"title\": {\n      \"_errors\": [\"Required\"]\n    }\n  }\n}", "description": "### 参数验证失败响应说明\n\n**触发条件**:\n- 缺少必填字段\n- 字段格式不正确\n- 文件类型不支持\n\n**处理建议**:\n- 检查所有必填字段\n- 验证文件格式和大小\n- 参考错误详情调整参数"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查认证状态", "if (!pm.cookies.has('connect.sid')) {", "    console.log('警告：未检测到Session Cookie，请先登录');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('创建小说响应验证', function () {", "    pm.expect(pm.response.code).to.be.oneOf([201, 400, 401]);", "});", "", "if (pm.response.code === 201) {", "    pm.test('创建成功响应格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('title');", "        pm.expect(jsonData).to.have.property('userId');", "        pm.expect(jsonData).to.have.property('createdAt');", "        pm.expect(jsonData).to.have.property('updatedAt');", "    });", "    ", "    // 保存新创建的小说ID供后续测试使用", "    const novelData = pm.response.json();", "    pm.environment.set('test_novel_id', novelData.id);", "}", "", "/* 业务逻辑验证：", "   - 验证返回的小说属于当前用户", "   - 检查默认状态设置正确", "   - 验证时间戳格式", "*/"], "type": "text/javascript"}}]}]}, {"name": "角色管理模块", "description": "### 角色管理模块\n\n#### 业务逻辑\n- 小说角色的创建、查看、编辑、删除\n- 角色头像上传和管理\n- 角色属性管理（姓名、描述、年龄、性别等）\n- 角色与小说的关联关系\n\n#### 业务约束\n- 角色必须属于某个小说\n- 用户只能管理自己小说中的角色\n- 管理员可以管理所有角色\n- 头像图片大小限制10MB\n- 支持的图片格式：jpg, jpeg, png, gif", "item": [{"name": "获取小说角色列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/{{novel_id}}", "host": ["{{base_url}}"], "path": ["api", "characters", "{{novel_id}}"], "variable": [{"key": "novel_id", "value": "1", "description": "小说ID"}]}, "description": "#### 获取小说角色列表\n\n**功能说明**: 获取指定小说的所有角色\n\n**权限要求**: \n- 角色：已登录用户\n- 权限：小说所有者或管理员\n\n**限流规则**: \n- QPS限制：50次/秒\n- 并发限制：20个/用户\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | Unauthorized | 重新登录 |\n| 403 | 403 | Forbidden | 检查小说访问权限 |\n| 404 | 404 | Novel not found | 确认小说ID正确 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 只能查看自己小说的角色\n- 管理员可以查看所有小说的角色\n- 按创建时间排序"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/characters/1"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"主角张三\",\n    \"description\": \"故事的主人公，勇敢善良\",\n    \"avatar\": \"/uploads/character-avatar-123.jpg\",\n    \"novelId\": 1,\n    \"createdAt\": \"2024-12-19T10:30:00.000Z\",\n    \"updatedAt\": \"2024-12-19T10:30:00.000Z\"\n  },\n  {\n    \"id\": 2,\n    \"name\": \"反派李四\",\n    \"description\": \"故事的反派角色，狡猾阴险\",\n    \"avatar\": null,\n    \"novelId\": 1,\n    \"createdAt\": \"2024-12-19T10:35:00.000Z\",\n    \"updatedAt\": \"2024-12-19T10:35:00.000Z\"\n  }\n]", "description": "### 角色列表响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| id | number | 角色唯一标识符 |\n| name | string | 角色姓名 |\n| description | string | 角色描述 |\n| avatar | string\\|null | 头像图片路径 |\n| novelId | number | 所属小说ID |\n| createdAt | string | 创建时间 |\n| updatedAt | string | 更新时间 |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查认证状态", "if (!pm.cookies.has('connect.sid')) {", "    console.log('警告：未检测到Session Cookie，请先登录');", "}", "", "// 使用环境变量中的小说ID", "if (pm.environment.get('test_novel_id')) {", "    pm.variables.set('novel_id', pm.environment.get('test_novel_id'));", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('获取角色列表成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('响应格式验证', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    ", "    if (jsonData.length > 0) {", "        const character = jsonData[0];", "        pm.expect(character).to.have.property('id');", "        pm.expect(character).to.have.property('name');", "        pm.expect(character).to.have.property('novelId');", "        pm.expect(character).to.have.property('createdAt');", "    }", "});", "", "/* 业务逻辑验证：", "   - 验证返回的角色都属于指定小说", "   - 检查角色数据完整性", "   - 验证时间格式正确性", "*/"], "type": "text/javascript"}}]}]}, {"name": "管理员模块", "description": "### 管理员模块\n\n#### 业务逻辑\n- 用户管理（查看、编辑、删除用户）\n- 权限管理（设置/撤销管理员权限）\n- 系统统计信息查看\n- 全局数据管理\n\n#### 业务约束\n- 仅管理员可以访问\n- 不能删除自己的账户\n- 至少保留一个管理员账户\n- 系统统计数据实时计算", "item": [{"name": "获取系统统计信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/admin/stats", "host": ["{{base_url}}"], "path": ["api", "admin", "stats"]}, "description": "#### 获取系统统计信息\n\n**功能说明**: 获取系统整体运营数据统计\n\n**权限要求**: \n- 角色：管理员\n- 权限：需要管理员权限\n\n**限流规则**: \n- QPS限制：10次/秒\n- 并发限制：5个/管理员\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 401 | 401 | Unauthorized | 重新登录 |\n| 403 | 403 | Forbidden | 需要管理员权限 |\n| 500 | 500 | 服务器内部错误 | 联系技术支持 |\n\n**业务规则**:\n- 统计数据实时计算\n- 包含用户、小说、角色、关系等核心指标\n- 仅管理员可以查看"}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/admin/stats"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"userCount\": 25,\n  \"novelCount\": 156,\n  \"characterCount\": 892,\n  \"relationshipCount\": 1247,\n  \"genreCount\": 18\n}", "description": "### 系统统计响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| userCount | number | 总用户数 |\n| novelCount | number | 总小说数 |\n| characterCount | number | 总角色数 |\n| relationshipCount | number | 总关系数 |\n| genreCount | number | 总类型数 |"}], "event": [{"listen": "prerequest", "script": {"exec": ["// 检查管理员权限", "if (!pm.cookies.has('connect.sid')) {", "    console.log('警告：未检测到Session Cookie，请先登录');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('获取统计信息成功', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 403]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('统计数据格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('userCount');", "        pm.expect(jsonData).to.have.property('novelCount');", "        pm.expect(jsonData).to.have.property('characterCount');", "        pm.expect(jsonData).to.have.property('relationshipCount');", "        pm.expect(jsonData).to.have.property('genreCount');", "        ", "        // 验证数据类型", "        pm.expect(jsonData.userCount).to.be.a('number');", "        pm.expect(jsonData.novelCount).to.be.a('number');", "    });", "}"], "type": "text/javascript"}}]}]}, {"name": "微信读书API代理", "description": "### 微信读书API代理\n\n#### 业务逻辑\n- 代理微信读书搜索API\n- 提供书籍信息检索服务\n- 支持关键词搜索\n- 返回标准化的书籍数据\n\n#### 业务约束\n- 无需登录即可访问\n- 搜索关键词不能为空\n- 依赖外部API服务可用性\n- 返回数据格式由外部API决定", "item": [{"name": "搜索书籍", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=三体", "host": ["{{base_url}}"], "path": ["api", "weread", "search"], "query": [{"key": "keyword", "value": "三体", "description": "搜索关键词"}]}, "description": "#### 搜索书籍\n\n**功能说明**: 通过微信读书API搜索书籍信息\n\n**权限要求**: \n- 角色：无需认证\n- 权限：公开接口\n\n**限流规则**: \n- QPS限制：30次/秒\n- 并发限制：15个/IP\n\n**错误码对照表**:\n\n| 错误码 | HTTP状态码 | 错误信息 | 解决方案 |\n|--------|------------|----------|----------|\n| 400 | 400 | 搜索关键词不能为空 | 提供有效的搜索关键词 |\n| 500 | 500 | 微信读书API错误 | 稍后重试或联系技术支持 |\n| 502 | 502 | 外部服务不可用 | 稍后重试 |\n\n**业务规则**:\n- 关键词长度至少1个字符\n- 返回数据格式由微信读书API决定\n- 支持中文和英文搜索"}, "response": [{"name": "搜索成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword=三体"}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"books\": [\n    {\n      \"bookId\": \"12345\",\n      \"title\": \"三体\",\n      \"author\": \"刘慈欣\",\n      \"cover\": \"https://example.com/cover.jpg\",\n      \"intro\": \"科幻小说经典之作...\",\n      \"category\": \"科幻\",\n      \"publishTime\": \"2006-05-01\"\n    },\n    {\n      \"bookId\": \"12346\",\n      \"title\": \"三体II：黑暗森林\",\n      \"author\": \"刘慈欣\",\n      \"cover\": \"https://example.com/cover2.jpg\",\n      \"intro\": \"三体系列第二部...\",\n      \"category\": \"科幻\",\n      \"publishTime\": \"2008-05-01\"\n    }\n  ],\n  \"totalCount\": 2\n}", "description": "### 搜索成功响应说明\n\n**响应字段**:\n\n| 字段名 | 类型 | 描述 |\n|--------|------|------|\n| books | array | 书籍列表 |\n| books[].bookId | string | 书籍ID |\n| books[].title | string | 书籍标题 |\n| books[].author | string | 作者 |\n| books[].cover | string | 封面图片URL |\n| books[].intro | string | 书籍简介 |\n| books[].category | string | 书籍分类 |\n| books[].publishTime | string | 出版时间 |\n| totalCount | number | 搜索结果总数 |"}, {"name": "搜索关键词为空", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/weread/search?keyword="}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"搜索关键词不能为空\"\n}", "description": "### 参数错误响应说明\n\n**触发条件**:\n- 未提供keyword参数\n- keyword参数为空字符串\n\n**处理建议**:\n- 确保提供有效的搜索关键词\n- 关键词长度至少1个字符"}], "event": [{"listen": "test", "script": {"exec": ["pm.test('搜索响应验证', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 500]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('搜索结果格式验证', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('books');", "        pm.expect(jsonData.books).to.be.an('array');", "        ", "        if (jsonData.books.length > 0) {", "            const book = jsonData.books[0];", "            pm.expect(book).to.have.property('bookId');", "            pm.expect(book).to.have.property('title');", "            pm.expect(book).to.have.property('author');", "        }", "    });", "}"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"exec": ["// 全局预请求脚本", "// 设置请求时间戳", "pm.globals.set('timestamp', new Date().getTime());"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 全局测试脚本", "// 记录API调用日志", "console.log(`API调用: ${pm.request.method} ${pm.request.url}`);"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:5001", "description": "API服务器基础URL，根据环境切换（开发/测试/生产）"}, {"key": "api_version", "value": "v1", "description": "API版本号"}, {"key": "test_novel_id", "value": "", "description": "测试用小说ID，由创建小说接口自动设置"}, {"key": "test_character_id", "value": "", "description": "测试用角色ID，由创建角色接口自动设置"}, {"key": "novel_id", "value": "1", "description": "当前操作的小说ID"}]}