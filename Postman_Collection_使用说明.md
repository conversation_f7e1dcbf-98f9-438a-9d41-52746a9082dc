# 小说人物关系管理系统 API Collection 使用说明

## 概述

本文档提供了小说人物关系管理系统的完整API文档，以Postman Collection的形式提供，包含了所有API端点的详细说明、请求示例、响应格式和测试脚本。

## 文件信息

- **Collection文件**: `CharacterNetwork_API_Collection.json`
- **版本**: v1.0.0
- **格式**: Postman Collection v2.1.0
- **编码**: UTF-8
- **总接口数**: 10+ 个核心API端点
- **模块数**: 5个主要功能模块

## 导入步骤

### 1. 导入到Postman

1. 打开Postman应用
2. 点击左上角的"Import"按钮
3. 选择"File"选项卡
4. 点击"Upload Files"
5. 选择`CharacterNetwork_API_Collection.json`文件
6. 点击"Import"完成导入

### 2. 环境配置

导入后需要配置以下环境变量：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `base_url` | `http://localhost:5001` | API服务器地址 |
| `api_version` | `v1` | API版本号 |
| `test_novel_id` | `` | 测试小说ID（自动设置） |
| `test_character_id` | `` | 测试角色ID（自动设置） |
| `novel_id` | `1` | 当前操作的小说ID |

### 3. 环境切换

根据不同环境修改`base_url`：

- **开发环境**: `http://localhost:5001`
- **测试环境**: `https://test-api.characternetwork.com`
- **生产环境**: `https://api.characternetwork.com`

## API模块说明

### 1. 用户认证模块

包含用户注册、登录、登出和用户信息管理的API：

- **用户注册** - `POST /api/register`
- **用户登录** - `POST /api/login`
- **用户登出** - `POST /api/logout`
- **获取当前用户信息** - `GET /api/user`

**认证机制**: Session-based Authentication
**会话有效期**: 7天

### 2. 小说管理模块

小说的CRUD操作和文件上传：

- **获取用户小说列表** - `GET /api/novels`
- **创建小说** - `POST /api/novels` (支持文件上传)
- **获取小说详情** - `GET /api/novels/{id}`
- **更新小说** - `PUT /api/novels/{id}`
- **删除小说** - `DELETE /api/novels/{id}`

**文件上传**: 支持封面图片上传，最大10MB，支持jpg/jpeg/png/gif格式

### 3. 角色管理模块

小说角色的管理功能：

- **获取小说角色列表** - `GET /api/characters/{novelId}`
- **创建角色** - `POST /api/characters` (支持头像上传)
- **获取角色详情** - `GET /api/characters/single/{id}`
- **更新角色** - `PUT /api/characters/{id}`
- **删除角色** - `DELETE /api/characters/{id}`

### 4. 管理员模块

系统管理功能（仅管理员可访问）：

- **获取系统统计信息** - `GET /api/admin/stats`
- **用户管理** - 用户的增删改查
- **权限管理** - 设置/撤销管理员权限

### 5. 微信读书API代理

外部API集成：

- **搜索书籍** - `GET /api/weread/search?keyword={keyword}`

**特点**: 无需认证，公开访问

## 使用流程

### 基础测试流程

1. **用户注册**
   ```
   POST /api/register
   {
     "username": "testuser",
     "password": "password123",
     "email": "<EMAIL>"
   }
   ```

2. **用户登录**
   ```
   POST /api/login
   {
     "username": "testuser",
     "password": "password123"
   }
   ```

3. **创建小说**
   ```
   POST /api/novels
   FormData: {
     "title": "我的小说",
     "description": "小说描述",
     "genre": "奇幻"
   }
   ```

4. **创建角色**
   ```
   POST /api/characters
   FormData: {
     "name": "主角",
     "description": "主人公",
     "novelId": 1
   }
   ```

### 自动化测试

Collection中包含了完整的测试脚本：

- **预请求脚本**: 自动检查认证状态
- **测试脚本**: 验证响应格式和业务逻辑
- **环境变量管理**: 自动保存测试数据ID

## 错误处理

### 常见错误码

| HTTP状态码 | 错误类型 | 处理建议 |
|------------|----------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未认证 | 重新登录获取有效Session |
| 403 | 权限不足 | 检查用户权限或联系管理员 |
| 404 | 资源不存在 | 确认资源ID正确 |
| 413 | 文件过大 | 减小文件大小至10MB以下 |
| 415 | 文件类型不支持 | 使用支持的图片格式 |
| 500 | 服务器错误 | 联系技术支持 |

### 调试技巧

1. **查看Console输出**: Postman Console中会显示详细的调试信息
2. **检查环境变量**: 确保环境变量设置正确
3. **验证Cookie**: 确保Session Cookie正确传递
4. **文件上传**: 注意文件大小和格式限制

## 高级功能

### 批量测试

使用Postman的Collection Runner功能：

1. 点击Collection右侧的"..."菜单
2. 选择"Run collection"
3. 配置测试参数和环境
4. 运行完整的API测试套件

### 自动化CI/CD

使用Newman命令行工具：

```bash
# 安装Newman
npm install -g newman

# 运行测试
newman run CharacterNetwork_API_Collection.json \
  --environment your-environment.json \
  --reporters cli,html \
  --reporter-html-export report.html
```

## 技术支持

- **开发团队**: CharacterNetwork开发团队
- **联系邮箱**: <EMAIL>
- **文档更新**: 2024年12月
- **API版本**: v1.0.0

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 包含用户认证、小说管理、角色管理等核心功能
- 完整的API文档和测试脚本
- 支持文件上传和外部API集成

---

**注意**: 请确保在使用前已正确启动后端服务，并根据实际部署环境调整`base_url`配置。
