# 小说人物关系管理系统 Postman API Collection

## 📋 项目概述

这是一个完整的小说人物关系管理系统API文档，以Postman Collection的形式提供。该系统允许用户创建和管理小说、角色、人物关系等信息，支持文件上传、外部API集成等功能。

## 🎯 主要特性

- ✅ **完整的API文档** - 覆盖所有核心功能模块
- ✅ **详细的请求示例** - 每个API都有完整的请求参数说明
- ✅ **丰富的响应示例** - 包含成功和错误响应的详细说明
- ✅ **自动化测试脚本** - 内置完整的API测试验证
- ✅ **中文文档** - 全中文注释和说明
- ✅ **业务导向** - 注重业务逻辑和实际使用场景
- ✅ **Postman兼容** - 严格遵循Postman Collection v2.1.0标准

## 📁 文件列表

| 文件名 | 描述 | 大小 |
|--------|------|------|
| `CharacterNetwork_API_Collection.json` | 主要的Postman Collection文件 | ~1000行 |
| `Postman_Collection_使用说明.md` | 详细的使用说明文档 | 完整指南 |
| `API_端点总览.md` | API端点快速参考表 | 总览表格 |
| `validate_collection.py` | Collection验证脚本 | Python脚本 |
| `Postman_Collection_README.md` | 项目说明文档 | 本文件 |

## 🚀 快速开始

### 1. 导入Collection

1. 打开Postman应用
2. 点击"Import"按钮
3. 选择`CharacterNetwork_API_Collection.json`文件
4. 完成导入

### 2. 配置环境

设置以下环境变量：

```json
{
  "base_url": "http://localhost:5001",
  "api_version": "v1"
}
```

### 3. 开始测试

按照以下顺序测试API：

1. **用户注册** → **用户登录**
2. **创建小说** → **创建角色**
3. **创建关系** → **管理数据**

## 📊 Collection统计

根据验证脚本的结果：

- 📁 **模块数量**: 5个
- 🔗 **API端点数量**: 9个核心端点
- 📄 **响应示例数量**: 14个
- 🧪 **测试脚本数量**: 9个
- 🔧 **环境变量数量**: 5个

## 🏗️ 系统架构

### 技术栈
- **后端**: Express.js + TypeScript
- **数据库**: PostgreSQL + Drizzle ORM
- **认证**: Session-based Authentication (Passport.js)
- **文件上传**: Multer (支持图片上传)

### 核心模块

#### 🔐 用户认证模块
- 用户注册、登录、登出
- Session会话管理
- 密码安全处理

#### 📚 小说管理模块
- 小说CRUD操作
- 封面图片上传
- 状态管理

#### 👥 角色管理模块
- 角色信息管理
- 头像上传
- 角色属性设置

#### 🔧 管理员模块
- 用户管理
- 系统统计
- 权限控制

#### 🌐 外部API集成
- 微信读书API代理
- 书籍信息检索

## 🔒 安全特性

- **Session认证** - 基于Cookie的会话管理
- **权限控制** - 用户级和管理员级权限
- **文件验证** - 严格的文件类型和大小限制
- **参数验证** - 完整的请求参数验证
- **错误处理** - 统一的错误响应格式

## 📝 API设计规范

### 请求格式
- **Content-Type**: `application/json` 或 `multipart/form-data`
- **认证**: Session Cookie
- **编码**: UTF-8

### 响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "errorCode": null
}
```

### 错误处理
```json
{
  "success": false,
  "message": "错误描述",
  "errorCode": "ERROR_CODE",
  "details": {}
}
```

## 🧪 测试功能

### 自动化测试
- **响应状态验证** - 检查HTTP状态码
- **数据格式验证** - 验证响应数据结构
- **业务逻辑验证** - 检查业务规则执行
- **性能测试** - 响应时间验证

### 测试覆盖
- ✅ 用户认证流程
- ✅ 数据CRUD操作
- ✅ 文件上传功能
- ✅ 权限控制验证
- ✅ 错误处理测试

## 📈 使用场景

### 开发阶段
- API接口调试
- 功能验证测试
- 集成测试

### 测试阶段
- 自动化测试执行
- 回归测试验证
- 性能基准测试

### 生产阶段
- API监控
- 问题排查
- 文档参考

## 🔧 高级功能

### Collection Runner
使用Postman的Collection Runner执行批量测试：

1. 选择Collection
2. 配置环境变量
3. 设置测试数据
4. 运行完整测试套件

### Newman CLI
使用Newman命令行工具进行CI/CD集成：

```bash
npm install -g newman
newman run CharacterNetwork_API_Collection.json \
  --environment production.json \
  --reporters cli,html
```

## 📞 技术支持

- **开发团队**: CharacterNetwork开发团队
- **联系邮箱**: <EMAIL>
- **项目地址**: [GitHub Repository]
- **文档版本**: v1.0.0
- **更新时间**: 2024年12月19日

## 📄 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和测试人员。

---

**注意**: 使用前请确保后端服务已正确启动，并根据实际部署环境调整`base_url`配置。
