# 小说人物关系管理系统 API 端点总览

## 基础信息

- **基础URL**: `http://localhost:5001`
- **认证方式**: Session-based Authentication
- **数据格式**: JSON / FormData (文件上传)
- **字符编码**: UTF-8

## API端点列表

### 🔐 用户认证模块

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| POST | `/api/register` | 用户注册 | ❌ | ❌ |
| POST | `/api/login` | 用户登录 | ❌ | ❌ |
| POST | `/api/logout` | 用户登出 | ✅ | ❌ |
| GET | `/api/user` | 获取当前用户信息 | ✅ | ❌ |
| POST | `/api/change-password` | 修改密码 | ✅ | ❌ |

### 📚 小说管理模块

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/novels` | 获取用户小说列表 | ✅ | ❌ |
| GET | `/api/novels/{id}` | 获取小说详情 | ✅ | ❌ |
| POST | `/api/novels` | 创建小说 | ✅ | ✅ (封面) |
| PUT | `/api/novels/{id}` | 更新小说 | ✅ | ✅ (封面) |
| DELETE | `/api/novels/{id}` | 删除小说 | ✅ | ❌ |
| POST | `/api/novels/from-book/{externalId}` | 从外部书籍创建小说 | ✅ | ❌ |
| POST | `/api/novels/from-search-book` | 从搜索结果创建小说 | ✅ | ❌ |
| GET | `/api/novels/{novelId}/characters` | 获取小说角色列表 | ✅ | ❌ |
| GET | `/api/novels/{novelId}/relationships` | 获取小说关系列表 | ✅ | ❌ |

### 👥 角色管理模块

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/characters/{novelId}` | 获取小说角色列表 | ✅ | ❌ |
| GET | `/api/characters/single/{id}` | 获取角色详情 | ✅ | ❌ |
| POST | `/api/characters` | 创建角色 | ✅ | ✅ (头像) |
| PUT | `/api/characters/{id}` | 更新角色 | ✅ | ✅ (头像) |
| DELETE | `/api/characters/{id}` | 删除角色 | ✅ | ❌ |

### 🔗 关系管理模块

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/relationships/{novelId}` | 获取小说关系列表 | ✅ | ❌ |
| GET | `/api/relationships/single/{id}` | 获取关系详情 | ✅ | ❌ |
| POST | `/api/relationships` | 创建关系 | ✅ | ❌ |
| PUT | `/api/relationships/{id}` | 更新关系 | ✅ | ❌ |
| DELETE | `/api/relationships/{id}` | 删除关系 | ✅ | ❌ |

### 🏷️ 关系类型管理

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/relationship-types` | 获取关系类型列表 | ✅ | ❌ |
| POST | `/api/relationship-types` | 创建关系类型 | ✅ | ❌ |
| PUT | `/api/relationship-types/{id}` | 更新关系类型 | ✅ | ❌ |
| DELETE | `/api/relationship-types/{id}` | 删除关系类型 | ✅ | ❌ |

### 📖 小说类型管理

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/genres` | 获取用户类型列表 | ✅ | ❌ |
| GET | `/api/genres/public` | 获取公共类型列表 | ❌ | ❌ |
| GET | `/api/genres/{id}` | 获取类型详情 | ✅ | ❌ |
| POST | `/api/genres` | 创建类型 | ✅ | ❌ |
| PUT | `/api/genres/{id}` | 更新类型 | ✅ | ❌ |
| DELETE | `/api/genres/{id}` | 删除类型 | ✅ | ❌ |
| GET | `/api/genres/admin/all` | 获取所有类型(管理员) | 🔑 | ❌ |

### 📚 书籍信息管理

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/books/{id}` | 获取书籍信息 | ✅ | ❌ |
| GET | `/api/books/external/{externalId}` | 通过外部ID获取书籍 | ✅ | ❌ |
| GET | `/api/books/search/{query}` | 搜索书籍信息 | ✅ | ❌ |
| POST | `/api/books` | 创建书籍信息 | ✅ | ❌ |
| PUT | `/api/books/{id}` | 更新书籍信息 | 🔑 | ❌ |
| DELETE | `/api/books/{id}` | 删除书籍信息 | 🔑 | ❌ |
| GET | `/api/books/{id}/novels` | 获取使用该书籍的小说 | 🔑 | ❌ |

### ⏰ 时间线事件管理

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/novels/{novelId}/timeline-events` | 获取小说时间线事件 | ✅ | ❌ |
| POST | `/api/novels/{novelId}/timeline-events` | 创建时间线事件 | ✅ | ❌ |
| GET | `/api/timeline-events/{id}` | 获取时间线事件详情 | ✅ | ❌ |
| PUT | `/api/timeline-events/{id}` | 更新时间线事件 | ✅ | ❌ |
| DELETE | `/api/timeline-events/{id}` | 删除时间线事件 | ✅ | ❌ |

### 📝 笔记管理

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/novels/{novelId}/notes` | 获取小说笔记列表 | ✅ | ❌ |
| GET | `/api/notes/{id}` | 获取笔记详情 | ✅ | ❌ |
| POST | `/api/notes` | 创建笔记 | ✅ | ❌ |
| PUT | `/api/notes/{id}` | 更新笔记 | ✅ | ❌ |
| DELETE | `/api/notes/{id}` | 删除笔记 | ✅ | ❌ |

### 🔧 管理员模块

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/admin/users` | 获取所有用户 | 🔑 | ❌ |
| GET | `/api/admin/users/{id}` | 获取用户详情 | 🔑 | ❌ |
| PUT | `/api/admin/users/{id}` | 更新用户信息 | 🔑 | ❌ |
| DELETE | `/api/admin/users/{id}` | 删除用户 | 🔑 | ❌ |
| POST | `/api/admin/users/{id}/make-admin` | 设置管理员权限 | 🔑 | ❌ |
| POST | `/api/admin/users/{id}/revoke-admin` | 撤销管理员权限 | 🔑 | ❌ |
| GET | `/api/admin/stats` | 获取系统统计信息 | 🔑 | ❌ |

### 🌐 微信读书API代理

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/api/weread/search?keyword={keyword}` | 搜索书籍 | ❌ | ❌ |

### 📁 静态文件服务

| 方法 | 端点 | 功能 | 认证要求 | 文件上传 |
|------|------|------|----------|----------|
| GET | `/uploads/{filename}` | 获取上传的文件 | ❌ | ❌ |

## 图例说明

- ✅ 需要登录认证
- 🔑 需要管理员权限
- ❌ 无需认证/不支持
- ✅ (文件类型) 支持文件上传

## 限流规则

### 通用限制
- **QPS限制**: 100次/秒/用户
- **并发限制**: 50个/用户
- **文件大小**: 最大10MB
- **支持格式**: jpg, jpeg, png, gif

### 特殊限制
- **注册接口**: 10次/秒/IP
- **登录接口**: 20次/秒/IP
- **文件上传**: 5次/秒/用户
- **外部API**: 30次/秒/IP

## 错误码规范

### HTTP状态码
- **200**: 成功
- **201**: 创建成功
- **204**: 删除成功
- **400**: 请求参数错误
- **401**: 未认证
- **403**: 权限不足
- **404**: 资源不存在
- **413**: 文件过大
- **415**: 文件类型不支持
- **500**: 服务器内部错误

### 业务错误码
- **10001**: 参数缺失
- **10002**: 认证失败
- **10003**: 权限不足
- **10004**: 资源不存在
- **10005**: 资源冲突
- **10006**: 文件上传失败

## 数据模型

### 核心实体
- **User**: 用户
- **Novel**: 小说
- **Character**: 角色
- **Relationship**: 关系
- **RelationshipType**: 关系类型
- **NovelGenre**: 小说类型
- **BookInfo**: 书籍信息
- **TimelineEvent**: 时间线事件
- **Note**: 笔记

### 关联关系
- User 1:N Novel
- Novel 1:N Character
- Novel 1:N Relationship
- Novel 1:N TimelineEvent
- Novel 1:N Note
- Character N:N Relationship
- User 1:N RelationshipType
- User 1:N NovelGenre

---

**更新时间**: 2024年12月19日  
**API版本**: v1.0.0
